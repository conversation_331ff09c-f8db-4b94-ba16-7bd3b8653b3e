{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.5", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "ai": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "next": "15.0.3", "openai-edge": "^1.2.2", "plotly.js": "^2.35.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.1", "react-plotly.js": "^2.6.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/plotly.js": "^2.35.1", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-syntax-highlighter": "^15.5.13", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}